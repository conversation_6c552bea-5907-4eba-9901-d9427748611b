'use client'

import { useDomain } from '@/lib/hooks/use-domain';
import { applyCustomCSS, applyThemeColors, removeCustomStyles } from '@/lib/utils/cssOverride';
import { JSX, useEffect } from 'react';

/**
 * Component to apply domain-specific theme styles
 * This component should be placed high in the component tree
 * Note: This is now redundant with DomainContext but kept for backward compatibility
 */
export function DomainTheme(): JSX.Element | null {
  const { config } = useDomain()

  useEffect(() => {
    if (config?.theme) {
      // Apply theme colors using the utility function
      applyThemeColors(config.theme)
    }

    if (config?.custom_css && Object.keys(config.custom_css).length > 0) {
      // Apply custom CSS using a utility function


      // Return cleanup function
      return applyCustomCSS(config.custom_css)
    }

    // Cleanup when no config
    return () => {
      if (!config) {
        removeCustomStyles()
      }
    }
  }, [config])

  return null
}
