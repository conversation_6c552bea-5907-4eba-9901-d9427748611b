'use client'

import { Icons } from '@/components/icons'
import { queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useRouter, useSearchParams } from 'next/navigation'
import { use, useEffect, useState } from 'react'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { useMutation } from '@tanstack/react-query'
import { toast } from 'sonner'

interface ComponentProps {
  params:
    | {
        slug: string[]
      }
    | Promise<{ slug: string[] }>
}

export default function Component({ params }: ComponentProps) {
  const resolvedParams = params instanceof Promise ? use(params) : params

  const router = useRouter()
  const searchParams = useSearchParams()
  const verificationUrl = resolvedParams.slug.join('/') + '?' + searchParams.toString()

  const [reset, setVerified] = useState(false)
  const [data, setData] = useState<ApiResponse>()

  const { isPending, mutate } = useMutation({
    mutationFn: (verificationUrl: string) =>
      queryFetchHelper('/password/' + verificationUrl, {
        method: 'GET',
      }),
    onSuccess: () => {
      router.push('/login')
      toast.success('Your password has been reset successfully. Please check your email to get your new password.')
    },
    onError: (error: ApiResponse) => {
      setData(error)
      toast.error(error.message)
    },
    onSettled: (data) => {
      setVerified(data?.success ?? false)
      if (data) {
        setData(data)
      }
    },
  })

  useEffect(() => {
    mutate(verificationUrl)
  }, [mutate, verificationUrl])

  return (
    <div className="flex flex-col">
      <h1 className="text-center text-4xl font-extrabold tracking-tight">Password Reset</h1>
      <div className="text-muted-foreground mt-6 text-center">
        {isPending && (
          <>
            <Icons.spinner className="mr-1 inline-block h-6 w-6 animate-spin" />
            Requesting...
          </>
        )}
        {reset && (
          <>
            <Icons.spinner className="mr-1 inline-block h-6 w-6 animate-spin" />
            Redirecting...
          </>
        )}
        {!reset && data && (
          <Alert
            variant="destructive"
            className="mx-auto w-fit text-center">
            <AlertDescription>{data.message || 'An error occurred while resetting your password.'}</AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  )
}
